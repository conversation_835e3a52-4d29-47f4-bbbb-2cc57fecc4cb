import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import InterviewPracticeInterface from '../InterviewPracticeInterface';

// Mock the callback functions
const mockOnResponseSubmit = jest.fn();
const mockOnSessionComplete = jest.fn();
const mockOnSessionPause = jest.fn();
const mockOnSessionExit = jest.fn();

// Mock session data
const mockSession = {
  id: 'test-session-id',
  sessionType: 'QUICK_PRACTICE',
  status: 'IN_PROGRESS',
  totalQuestions: 5,
  completedQuestions: 0,
  questions: [
    {
      id: 'question-1',
      questionText: 'Tell me about yourself.',
      questionType: 'BEHAVIORAL',
      category: 'GENERAL',
      difficulty: 'BEGINNER',
      expectedDuration: 180,
      context: 'This is a common opening question.',
      hints: {
        structure: 'Use the present-past-future structure',
        keyPoints: ['Current role', 'Relevant experience', 'Future goals'],
      },
      questionOrder: 1,
      responses: [],
    },
    {
      id: 'question-2',
      questionText: 'What are your strengths?',
      questionType: 'BEHAVIORAL',
      category: 'SOFT_SKILLS',
      difficulty: 'BEGINNER',
      expectedDuration: 120,
      questionOrder: 2,
      responses: [],
    },
  ],
  progress: {
    completed: 0,
    total: 5,
    percentage: 0,
  },
};

describe('InterviewPracticeInterface', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock navigator.mediaDevices for audio recording tests
    Object.defineProperty(navigator, 'mediaDevices', {
      writable: true,
      value: {
        getUserMedia: jest.fn().mockResolvedValue({
          getTracks: () => [],
        }),
      },
    });
  });

  it('renders the practice interface', () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    expect(screen.getByText('Interview Practice Session')).toBeInTheDocument();
    expect(screen.getByText('Question 1 of 5')).toBeInTheDocument();
  });

  it('displays the current question', () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    expect(screen.getByText('Tell me about yourself.')).toBeInTheDocument();
    expect(screen.getByText('BEHAVIORAL')).toBeInTheDocument();
    expect(screen.getByText('GENERAL')).toBeInTheDocument();
  });

  it('shows preparation timer initially', () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    expect(screen.getByText(/Prep:/)).toBeInTheDocument();
    expect(screen.getByText('Start Response')).toBeInTheDocument();
  });

  it('allows starting response after preparation', async () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    const startButton = screen.getByText('Start Response');
    fireEvent.click(startButton);

    await waitFor(() => {
      expect(screen.getByText(/Response:/)).toBeInTheDocument();
    });
  });

  it('allows typing a response', async () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    // Start response
    fireEvent.click(screen.getByText('Start Response'));

    await waitFor(() => {
      const textarea = screen.getByPlaceholderText('Type your response here...');
      fireEvent.change(textarea, { 
        target: { value: 'I am a software engineer with 5 years of experience...' } 
      });

      expect(textarea).toHaveValue('I am a software engineer with 5 years of experience...');
    });
  });

  it('shows context when context button is clicked', async () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    const contextButton = screen.getByRole('button', { name: /show context/i });
    fireEvent.click(contextButton);

    await waitFor(() => {
      expect(screen.getByText('This is a common opening question.')).toBeInTheDocument();
    });
  });

  it('shows hints when hints button is clicked', async () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    const hintsButton = screen.getByRole('button', { name: /lightbulb/i });
    fireEvent.click(hintsButton);

    await waitFor(() => {
      expect(screen.getByText('Use the present-past-future structure')).toBeInTheDocument();
      expect(screen.getByText('Current role')).toBeInTheDocument();
    });
  });

  it('validates response before submission', async () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    // Start response
    fireEvent.click(screen.getByText('Start Response'));

    await waitFor(() => {
      // Try to submit without typing a response
      const submitButton = screen.getByText('Submit & Next');
      expect(submitButton).toBeDisabled();
    });
  });

  it('enables submit button when response is provided', async () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    // Start response
    fireEvent.click(screen.getByText('Start Response'));

    await waitFor(() => {
      const textarea = screen.getByPlaceholderText('Type your response here...');
      fireEvent.change(textarea, { 
        target: { value: 'I am a software engineer with experience in React and Node.js.' } 
      });

      const submitButton = screen.getByText('Submit & Next');
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('calls onResponseSubmit when response is submitted', async () => {
    mockOnResponseSubmit.mockResolvedValue(undefined);

    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    // Start response
    fireEvent.click(screen.getByText('Start Response'));

    await waitFor(() => {
      const textarea = screen.getByPlaceholderText('Type your response here...');
      fireEvent.change(textarea, { 
        target: { value: 'Test response' } 
      });

      const submitButton = screen.getByText('Submit & Next');
      fireEvent.click(submitButton);
    });

    await waitFor(() => {
      expect(mockOnResponseSubmit).toHaveBeenCalledWith('question-1', {
        responseText: 'Test response',
        responseTime: expect.any(Number),
        preparationTime: expect.any(Number),
      });
    });
  });

  it('allows navigation between questions', () => {
    const sessionWithMultipleQuestions = {
      ...mockSession,
      completedQuestions: 1,
    };

    render(
      <InterviewPracticeInterface
        session={sessionWithMultipleQuestions}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    const nextButton = screen.getByText('Next');
    expect(nextButton).toBeInTheDocument();
  });

  it('calls onSessionPause when pause button is clicked', () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    const pauseButton = screen.getByText('Pause');
    fireEvent.click(pauseButton);

    expect(mockOnSessionPause).toHaveBeenCalledTimes(1);
  });

  it('calls onSessionExit when exit button is clicked', () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    const exitButton = screen.getByText('Exit');
    fireEvent.click(exitButton);

    expect(mockOnSessionExit).toHaveBeenCalledTimes(1);
  });

  it('shows progress bar', () => {
    render(
      <InterviewPracticeInterface
        session={mockSession}
        onResponseSubmit={mockOnResponseSubmit}
        onSessionComplete={mockOnSessionComplete}
        onSessionPause={mockOnSessionPause}
        onSessionExit={mockOnSessionExit}
      />
    );

    // Progress bar should be present (0% initially)
    const progressBar = screen.getByRole('progressbar');
    expect(progressBar).toBeInTheDocument();
  });
});
