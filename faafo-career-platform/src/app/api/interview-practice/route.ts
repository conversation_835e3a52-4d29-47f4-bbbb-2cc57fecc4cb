import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withError<PERSON>and<PERSON> } from '@/lib/errorHandler';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';

// Validation schema for creating interview sessions
const createSessionSchema = z.object({
  sessionType: z.enum(['QUICK_PRACTICE', 'FOCUSED_SESSION', 'MOCK_INTERVIEW', 'BEHAVIORAL_PRACTICE', 'TECHNICAL_PRACTICE', 'CUSTOM_SESSION']),
  careerPath: z.string().optional(),
  experienceLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
  companyType: z.string().optional(),
  industryFocus: z.string().optional(),
  specificRole: z.string().optional(),
  interviewType: z.enum(['PHONE', 'VIDEO', 'IN_PERSON', 'PANEL', 'GROUP', 'TECHNICAL_SCREEN', 'BEHAVIORAL', 'CASE_STUDY']).optional(),
  preparationTime: z.string().optional(),
  focusAreas: z.array(z.string()).optional(),
  difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).default('BEGINNER'),
  totalQuestions: z.number().min(1).max(50).default(10),
});

// GET - Retrieve user's interview sessions
export async function GET(request: NextRequest) {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 30 }, // 30 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;
      const { searchParams } = new URL(request.url);
      const status = searchParams.get('status');
      const limit = parseInt(searchParams.get('limit') || '10');
      const offset = parseInt(searchParams.get('offset') || '0');

      try {
        const whereClause: any = { userId };
        if (status) {
          whereClause.status = status;
        }

        const [sessions, total] = await Promise.all([
          prisma.interviewSession.findMany({
            where: whereClause,
            include: {
              questions: {
                select: {
                  id: true,
                  questionType: true,
                  category: true,
                  difficulty: true,
                  questionOrder: true,
                },
                orderBy: { questionOrder: 'asc' }
              },
              responses: {
                select: {
                  id: true,
                  questionId: true,
                  isCompleted: true,
                  aiScore: true,
                },
              },
            },
            orderBy: { createdAt: 'desc' },
            take: limit,
            skip: offset,
          }),
          prisma.interviewSession.count({ where: whereClause }),
        ]);

        return NextResponse.json({
          success: true,
          data: {
            sessions,
            pagination: {
              total,
              limit,
              offset,
              hasMore: offset + limit < total,
            },
          },
        });
      } catch (error) {
        console.error('Error fetching interview sessions:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to fetch interview sessions' },
          { status: 500 }
        );
      }
    }
  );
}

// POST - Create new interview session
export const POST = withErrorHandler(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 sessions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json(
          { success: false, error: 'Authentication required' },
          { status: 401 }
        );
      }

      const userId = session.user.id;

      try {
        const body = await request.json();
        const validation = createSessionSchema.safeParse(body);
        
        if (!validation.success) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Invalid request data',
              details: validation.error.errors 
            },
            { status: 400 }
          );
        }

        const sessionData = validation.data;

        // Create the interview session
        const interviewSession = await prisma.interviewSession.create({
          data: {
            userId,
            sessionType: sessionData.sessionType,
            careerPath: sessionData.careerPath,
            experienceLevel: sessionData.experienceLevel,
            companyType: sessionData.companyType,
            industryFocus: sessionData.industryFocus,
            specificRole: sessionData.specificRole,
            interviewType: sessionData.interviewType,
            preparationTime: sessionData.preparationTime,
            focusAreas: sessionData.focusAreas,
            difficulty: sessionData.difficulty,
            totalQuestions: sessionData.totalQuestions,
            status: 'IN_PROGRESS',
            sessionConfig: {
              createdVia: 'api',
              userAgent: request.headers.get('user-agent'),
            },
          },
          include: {
            questions: true,
            responses: true,
          },
        });

        return NextResponse.json({
          success: true,
          data: interviewSession,
          message: 'Interview session created successfully',
        });
      } catch (error) {
        console.error('Error creating interview session:', error);
        return NextResponse.json(
          { success: false, error: 'Failed to create interview session' },
          { status: 500 }
        );
      }
    }
  );
});
