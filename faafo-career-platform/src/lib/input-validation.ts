import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

/**
 * Comprehensive input validation and sanitization utilities
 * Addresses security issues identified by testerat
 */

// Common validation patterns
const PATTERNS = {
  // Basic text with no HTML
  SAFE_TEXT: /^[a-zA-Z0-9\s\-_.,!?()'"]+$/,
  // Professional text (allows more punctuation)
  PROFESSIONAL_TEXT: /^[a-zA-Z0-9\s\-_.,!?()'"@#$%&*+=:;/\\[\]{}|~`]+$/,
  // Email validation
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  // UUID validation
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  // URL validation
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
};

// Malicious patterns to detect and block
const MALICIOUS_PATTERNS = [
  // XSS patterns
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
  /javascript:/gi,
  /vbscript:/gi,
  /onload\s*=/gi,
  /onerror\s*=/gi,
  /onclick\s*=/gi,
  /onmouseover\s*=/gi,
  
  // SQL injection patterns
  /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
  /(--|#|\/\*|\*\/)/g,
  /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi,
  
  // Path traversal patterns
  /\.\.\//g,
  /\.\.\\\\g,
  /%2e%2e%2f/gi,
  /%2e%2e%5c/gi,
  
  // Template injection patterns
  /\{\{.*\}\}/g,
  /\$\{.*\}/g,
  /%\{.*\}/g,
];

/**
 * Sanitize text input by removing/escaping dangerous content
 */
export function sanitizeText(input: string, options: {
  allowHtml?: boolean;
  maxLength?: number;
  stripWhitespace?: boolean;
} = {}): string {
  if (!input || typeof input !== 'string') {
    return '';
  }

  let sanitized = input;

  // Trim whitespace if requested
  if (options.stripWhitespace) {
    sanitized = sanitized.trim();
  }

  // Truncate if max length specified
  if (options.maxLength && sanitized.length > options.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength);
  }

  // Handle HTML content
  if (options.allowHtml) {
    // Use DOMPurify to sanitize HTML
    sanitized = DOMPurify.sanitize(sanitized, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: [],
      KEEP_CONTENT: true,
    });
  } else {
    // Strip all HTML tags
    sanitized = sanitized.replace(/<[^>]*>/g, '');
  }

  // Remove null bytes and other control characters
  sanitized = sanitized.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

  return sanitized;
}

/**
 * Validate input against malicious patterns
 */
export function validateSecurity(input: string): {
  isValid: boolean;
  threats: string[];
  sanitized: string;
} {
  const threats: string[] = [];
  let sanitized = input;

  // Check for malicious patterns
  MALICIOUS_PATTERNS.forEach((pattern, index) => {
    if (pattern.test(input)) {
      switch (index) {
        case 0:
        case 1:
          threats.push('XSS script injection detected');
          break;
        case 2:
        case 3:
          threats.push('JavaScript protocol detected');
          break;
        case 4:
        case 5:
        case 6:
        case 7:
          threats.push('Event handler injection detected');
          break;
        case 8:
        case 9:
        case 10:
        case 11:
          threats.push('SQL injection pattern detected');
          break;
        case 12:
        case 13:
        case 14:
        case 15:
          threats.push('Path traversal attempt detected');
          break;
        case 16:
        case 17:
        case 18:
          threats.push('Template injection detected');
          break;
      }
      
      // Remove the malicious content
      sanitized = sanitized.replace(pattern, '');
    }
  });

  return {
    isValid: threats.length === 0,
    threats,
    sanitized: sanitizeText(sanitized, { stripWhitespace: true }),
  };
}

/**
 * Interview practice specific validation schemas
 */
export const InterviewValidationSchemas = {
  // Response text validation
  responseText: z.string()
    .min(10, 'Response must be at least 10 characters')
    .max(5000, 'Response cannot exceed 5000 characters')
    .refine((val) => {
      const security = validateSecurity(val);
      return security.isValid;
    }, 'Response contains potentially harmful content'),

  // User notes validation
  userNotes: z.string()
    .max(1000, 'Notes cannot exceed 1000 characters')
    .optional()
    .refine((val) => {
      if (!val) return true;
      const security = validateSecurity(val);
      return security.isValid;
    }, 'Notes contain potentially harmful content'),

  // Session configuration validation
  sessionConfig: z.object({
    sessionType: z.enum(['QUICK_PRACTICE', 'FOCUSED_SESSION', 'MOCK_INTERVIEW', 'BEHAVIORAL_PRACTICE', 'TECHNICAL_PRACTICE', 'CUSTOM_SESSION']),
    careerPath: z.string().max(100).optional(),
    experienceLevel: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).optional(),
    companyType: z.string().max(100).optional(),
    industryFocus: z.string().max(100).optional(),
    specificRole: z.string().max(100).optional(),
    interviewType: z.enum(['PHONE', 'VIDEO', 'IN_PERSON', 'PANEL', 'GROUP', 'TECHNICAL_SCREEN', 'BEHAVIORAL', 'CASE_STUDY']).optional(),
    preparationTime: z.string().max(50).optional(),
    focusAreas: z.array(z.string().max(100)).max(10).optional(),
    difficulty: z.enum(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']).default('BEGINNER'),
    totalQuestions: z.number().min(1).max(50).default(10),
  }),

  // Time validation
  timeValue: z.number().min(0).max(7200), // Max 2 hours

  // UUID validation
  uuid: z.string().uuid('Invalid ID format'),
};

/**
 * Sanitize and validate interview response
 */
export function validateInterviewResponse(data: {
  responseText: string;
  userNotes?: string;
  responseTime: number;
  preparationTime: number;
}): {
  isValid: boolean;
  errors: string[];
  sanitizedData: typeof data;
} {
  const errors: string[] = [];
  const sanitizedData = { ...data };

  // Validate and sanitize response text
  const responseValidation = validateSecurity(data.responseText);
  if (!responseValidation.isValid) {
    errors.push(...responseValidation.threats);
  }
  sanitizedData.responseText = sanitizeText(responseValidation.sanitized, {
    maxLength: 5000,
    stripWhitespace: true,
  });

  // Validate and sanitize user notes
  if (data.userNotes) {
    const notesValidation = validateSecurity(data.userNotes);
    if (!notesValidation.isValid) {
      errors.push(...notesValidation.threats);
    }
    sanitizedData.userNotes = sanitizeText(notesValidation.sanitized, {
      maxLength: 1000,
      stripWhitespace: true,
    });
  }

  // Validate time values
  if (data.responseTime < 0 || data.responseTime > 7200) {
    errors.push('Invalid response time');
  }
  if (data.preparationTime < 0 || data.preparationTime > 1800) {
    errors.push('Invalid preparation time');
  }

  // Check minimum response length
  if (sanitizedData.responseText.length < 10) {
    errors.push('Response is too short');
  }

  return {
    isValid: errors.length === 0,
    errors,
    sanitizedData,
  };
}

/**
 * Rate limiting for input validation
 */
const validationAttempts = new Map<string, { count: number; resetTime: number }>();

export function checkValidationRateLimit(identifier: string, maxAttempts = 10, windowMs = 60000): boolean {
  const now = Date.now();
  const entry = validationAttempts.get(identifier) || { count: 0, resetTime: now + windowMs };

  if (now > entry.resetTime) {
    entry.count = 0;
    entry.resetTime = now + windowMs;
  }

  entry.count++;
  validationAttempts.set(identifier, entry);

  return entry.count <= maxAttempts;
}
